<?php

// Include configuration file
require_once 'config.php';
require_once 'JsonDatabase.php';

define('API_KEY', $API_KEY);

// Initialize JSON Database
$db = new JsonDatabase($JSON_DB_DIR);

/**
 * Function to find voucher by code in the old format (from callback.php)
 */
function findVoucherByCode($voucher_code) {
    $voucherFile = "data/vouchers/{$voucher_code}.json";
    if (file_exists($voucherFile)) {
        return json_decode(file_get_contents($voucherFile), true);
    }
    return null;
}

//-----------------------------------------------------------------------------------------
// Payment Gateway Functions (Zibal)

// Function to get voucher price (10,000 Toman per voucher + 7,000 commission)
function getVoucherPrice($count) {
    $voucherPrice = $count * 10000; // 10k per voucher
    $commission = 7000; // Fixed 7k commission
    return $voucherPrice + $commission;
}

// Function to convert Toman to Rial
function convertToRials($tomans) {
    return $tomans * 10;
}

// Function to generate payment link with Zibal gateway
function generatePaymentLink($user_id, $amount, $voucher_count) {
    global $db;

    // Zibal merchant key (replace with your actual key)
    $merchantKey = "zibal";

    // Create unique order ID - format: BV-TIMESTAMP-USERID
    $orderId = "BV-" . time() . "-" . $user_id;

    // Callback URL for payment return
    $callbackUrl = "https://speedx-team.ir/BitVoucher/callback.php";

    // Payment description
    $description = "خرید $voucher_count ووچر BitVoucher برای کاربر $user_id";

    // Payment parameters
    $parameters = array(
        "merchant" => $merchantKey,
        "callbackUrl" => $callbackUrl,
        "amount" => convertToRials($amount), // Convert to Rial
        "orderId" => $orderId,
        "description" => $description
    );

    // Save order information
    $orderData = [
        'user_id' => $user_id,
        'voucher_count' => $voucher_count,
        'amount' => $amount,
        'status' => 'pending',
        'created_at' => time()
    ];
    $db->save('orders', $orderId, $orderData);

    // Create logs directory if not exists
    if (!file_exists("logs")) {
        mkdir("logs", 0777, true);
    }

    // Log payment request
    file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " Payment request: user_id=$user_id, voucher_count=$voucher_count, amount=$amount, orderId=$orderId\n", FILE_APPEND);

    // Send request to Zibal and get response
    $result = postToZibal('request', $parameters);

    // Log response
    file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " Response: " . json_encode($result) . "\n", FILE_APPEND);

    // Check response
    if ($result === false) {
        file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: Failed to connect to Zibal gateway\n", FILE_APPEND);
        return false;
    }

    if (!isset($result->result)) {
        file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: Invalid response from Zibal\n", FILE_APPEND);
        return false;
    }

    // If request successful, return payment link
    if ($result->result == 100) {
        if (isset($result->trackId)) {
            return "https://gateway.zibal.ir/start/" . $result->trackId;
        } else {
            file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: trackId not found in response\n", FILE_APPEND);
            return false;
        }
    }

    // In case of error
    $errorCode = $result->result ?? 'unknown';
    $errorMessage = isset($result->message) ? $result->message : 'Unknown error';

    file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: Zibal error code $errorCode: $errorMessage\n", FILE_APPEND);

    return false;
}

//-----------------------------------------------------------------------------------------

// Function to send request to Zibal API
function postToZibal($path, $parameters) {
    $url = 'https://gateway.zibal.ir/v1/' . $path;

    // Create logs directory if not exists
    if (!file_exists("logs")) {
        mkdir("logs", 0777, true);
    }

    // Log request
    file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " REQUEST to $url: " . json_encode($parameters) . "\n", FILE_APPEND);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // Log response
    file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " RESPONSE (HTTP $httpCode): $result\n", FILE_APPEND);

    if ($curlError) {
        file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " CURL ERROR: $curlError\n", FILE_APPEND);
        return false;
    }

    if ($httpCode !== 200) {
        file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " HTTP ERROR: Code $httpCode\n", FILE_APPEND);
        return false;
    }

    return json_decode($result);
}

//-----------------------------------------------------------------------------------------

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null, $parse_mode = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    if ($parse_mode) {
        $data['parse_mode'] = $parse_mode;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    // Create a single row with all channel buttons side by side
    $channel_buttons = [];
    $channel_names = ['کانال اول', 'کانال دوم'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }
    // Reverse the array to put "کانال اول" on the right
    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    // Add check membership button in a separate row
    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function createMainMenuKeyboard()
{
    $keyboard = [
        [
            [
                'text' => '👀 مشاهده توکن',
                'callback_data' => 'view_tokens'
            ],
            [
                'text' => '💸 خرید و فروش',
                'callback_data' => 'buy_sell'
            ]
        ],
        [
            [
                'text' => '💳 کیف پول',
                'callback_data' => 'wallet'
            ],
            [
                'text' => '☎️ پشتیبانی',
                'callback_data' => 'support'
            ]
        ],
        [
            [
                'text' => '📚 راهنما و مستندات',
                'callback_data' => 'help'
            ],
            [
                'text' => '⚙️ تنظیمات کاربری',
                'callback_data' => 'settings'
            ]
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

$update = json_decode(file_get_contents('php://input'));

// Handle callback queries (button presses)
if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    // Save/update user data
    $userData = [
        'first_name' => $callback_query->from->first_name ?? '',
        'last_name' => $callback_query->from->last_name ?? '',
        'username' => $callback_query->from->username ?? '',
        'last_interaction' => time(),
        'step' => 'checking_membership'
    ];

    if (!$db->exists('users', $user_id)) {
        $db->saveUser($user_id, $userData);
    } else {
        // Check if existing user has a token, if not generate one
        $existingUser = $db->getUser($user_id);
        if (!isset($existingUser['token']) || empty($existingUser['token'])) {
            $userData['token'] = $db->generateUserToken($user_id);
        }
        $db->update('users', $user_id, $userData);
    }

    if ($data == 'check_membership') {
        $join_keyboard = createJoinKeyboard($required_channels);

        // First, show checking message with buttons
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        // Small delay to show the checking message
        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            $first_name = $callback_query->from->first_name ?? 'کاربر';

            // Update user step to verified
            $db->updateUserStep($user_id, 'verified');

            $main_menu_text = "سلام $first_name 👋\n\n" .
                             "به ربات صرافی بیت ووچر خوش آمدید!\n\n" .
                             "با استفاده از این ربات شما می توانید پرداخت های تلگرامی خود را راحت تر انجام دهید.\n\n" .
                             "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";

            $main_menu_keyboard = createMainMenuKeyboard();

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => $main_menu_text,
                'parse_mode' => 'HTML',
                'reply_markup' => $main_menu_keyboard
            ]);
        } else {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "❌ شما هنوز عضو نیستید!\n\n" .
                         "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
        }
    }

    // Handle main menu button callbacks
    elseif (in_array($data, ['buy_sell', 'view_tokens', 'support', 'wallet', 'settings', 'help', 'buy_voucher', 'sell_voucher', 'increase_voucher', 'decrease_voucher', 'price_info', 'voucher_count', 'payment', 'back_to_main'])) {
        $first_name = $callback_query->from->first_name ?? 'کاربر';

        switch ($data) {
            case 'buy_sell':
                $response_text = "💸 بخش خرید و فروش\n\nلطفا یکی از گزینه‌های زیر را انتخاب کنید:";

                // Create buy/sell keyboard with buy button on the right
                $buy_sell_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '✱ فروش ووچر',
                                'callback_data' => 'sell_voucher'
                            ],
                            [
                                'text' => '✱ خرید ووچر',
                                'callback_data' => 'buy_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $buy_sell_keyboard
                ]);
                exit;
                break;
            case 'view_tokens':
                // Get user data to retrieve token
                $user = $db->getUser($user_id);
                $userToken = $user['token'] ?? 'توکن یافت نشد';

                $response_text = "👀 توکن شما :\n" .
                               "<code>$userToken</code>\n\n" .
                               "✱ از قرار دادن توکن خود به سایر افراد جداً خودداری کنید.\n" .
                               "✱ این توکن دائمی می باشد و منقضی نخواهد شد.\n" .
                               "✱ جهت اطلاعات بیشتر بخش مستندات را بخوانید.";

                // Create back button keyboard for token view
                $token_back_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'parse_mode' => 'HTML',
                    'reply_markup' => $token_back_keyboard
                ]);
                exit;
                break;
            case 'support':
                $response_text = "☎️ پشتیبانی\n\nبرای دریافت پشتیبانی با ما در ارتباط باشید.";
                break;
            case 'wallet':
                // Get user balance using helper method
                $balance = $db->getUserBalance($user_id);

                $response_text = "💳 کیف پول\n\n" .
                               "💰 موجودی شما: " . number_format($balance) . " تومان\n\n" .
                               "✱ برای افزایش موجودی، ووچرهای خود را بفروشید.\n" .
                               "✱ موجودی از طریق فروش ووچرهایی که خودتان ساخته‌اید، افزایش می‌یابد.";
                break;
            case 'settings':
                $response_text = "⚙️ تنظیمات کاربری\n\nاین بخش به زودی راه‌اندازی خواهد شد.";
                break;
            case 'help':
                $response_text = "📚 راهنما و مستندات\n\nاین بخش به زودی راه‌اندازی خواهد شد.";
                break;
            case 'buy_voucher':
                // Reset voucher count to 1 when entering buy voucher menu
                $user = $db->getUser($user_id);
                $userData = $user['data'] ?? [];
                $userData['voucher_count'] = 1;
                $db->updateUserData($user_id, $userData);

                $response_text = "💳 خرید ووچر\n\nلطفا مقدار ووچر که میخواهید خریداری کنید را تعیین کنید:";

                // Create voucher purchase keyboard
                $voucher_buy_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '✱ قیمت : 10هزارتومان ✱',
                                'callback_data' => 'price_info'
                            ]
                        ],
                        [
                            [
                                'text' => '➖',
                                'callback_data' => 'decrease_voucher'
                            ],
                            [
                                'text' => '1 ووچر',
                                'callback_data' => 'voucher_count'
                            ],
                            [
                                'text' => '➕',
                                'callback_data' => 'increase_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '✨ پرداخت',
                                'callback_data' => 'payment'
                            ],
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'buy_sell'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $voucher_buy_keyboard
                ]);
                exit;
                break;
            case 'sell_voucher':
                // Set user step to waiting for voucher code
                $db->updateUserStep($user_id, 'waiting_voucher_code');

                $response_text = "✱ فروش ووچر\n\n" .
                               "لطفا کد ووچری که می‌خواهید بفروشید را ارسال کنید:\n\n" .
                               "مثال: BV-XXXX-XXXX-XXXX-XXXX\n\n" .
                               "⚠️ توجه: فقط ووچرهایی که خودتان ساخته‌اید قابل فروش هستند.";

                // Create back button keyboard
                $back_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $back_keyboard
                ]);
                exit;
                break;
            case 'increase_voucher':
                // Get current voucher count from user data or default to 1
                $user = $db->getUser($user_id);
                $currentCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;
                $newCount = min($currentCount + 1, 10); // Max 10 vouchers

                // Update user data
                $userData = $user['data'] ?? [];
                $userData['voucher_count'] = $newCount;
                $db->updateUserData($user_id, $userData);

                // Update the message with new count
                $response_text = "💳 خرید ووچر\n\nلطفا مقدار ووچر که میخواهید خریداری کنید را تعیین کنید:";
                $totalPrice = $newCount * 10;

                $voucher_buy_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => "✱ قیمت : {$totalPrice}هزارتومان ✱",
                                'callback_data' => 'price_info'
                            ]
                        ],
                        [
                            [
                                'text' => '➖',
                                'callback_data' => 'decrease_voucher'
                            ],
                            [
                                'text' => "$newCount ووچر",
                                'callback_data' => 'voucher_count'
                            ],
                            [
                                'text' => '➕',
                                'callback_data' => 'increase_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '✨ پرداخت',
                                'callback_data' => 'payment'
                            ],
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'buy_sell'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $voucher_buy_keyboard
                ]);
                exit;
                break;
            case 'decrease_voucher':
                // Get current voucher count from user data or default to 1
                $user = $db->getUser($user_id);
                $currentCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;
                $newCount = max($currentCount - 1, 1); // Min 1 voucher

                // Update user data
                $userData = $user['data'] ?? [];
                $userData['voucher_count'] = $newCount;
                $db->updateUserData($user_id, $userData);

                // Update the message with new count
                $response_text = "💳 خرید ووچر\n\nلطفا مقدار ووچر که میخواهید خریداری کنید را تعیین کنید:";
                $totalPrice = $newCount * 10;

                $voucher_buy_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => "✱ قیمت : {$totalPrice}هزارتومان ✱",
                                'callback_data' => 'price_info'
                            ]
                        ],
                        [
                            [
                                'text' => '➖',
                                'callback_data' => 'decrease_voucher'
                            ],
                            [
                                'text' => "$newCount ووچر",
                                'callback_data' => 'voucher_count'
                            ],
                            [
                                'text' => '➕',
                                'callback_data' => 'increase_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '✨ پرداخت',
                                'callback_data' => 'payment'
                            ],
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'buy_sell'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $voucher_buy_keyboard
                ]);
                exit;
                break;
            case 'price_info':
            case 'voucher_count':
                // Do nothing, just ignore these clicks
                exit;
                break;
            case 'payment':
                // Get user's voucher count and calculate total
                $user = $db->getUser($user_id);
                $voucherCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;

                // Calculate prices using the payment function
                $totalPrice = getVoucherPrice($voucherCount);

                // Generate payment link
                $paymentLink = generatePaymentLink($user_id, $totalPrice, $voucherCount);

                if ($paymentLink) {
                    // Payment link generated successfully
                    $response_text = "✨ پرداخت ووچر\n\n" .
                                   "✱ تعداد ووچر: $voucherCount عدد\n" .
                                   "✱ کارمزد : 7هزارتومان\n" .
                                   "✱ مبلغ کل : " . number_format($totalPrice) . " تومان\n\n" .
                                   "🔗 برای پرداخت روی دکمه زیر کلیک کنید:";

                    // Create payment and back buttons
                    $payment_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '💳 پرداخت',
                                    'url' => $paymentLink
                                ]
                            ],
                            [
                                [
                                    'text' => '🔙 بازگشت',
                                    'callback_data' => 'buy_voucher'
                                ]
                            ]
                        ]
                    ]);
                } else {
                    // Payment link generation failed
                    $response_text = "❌ خطا در ایجاد لینک پرداخت\n\n" .
                                   "متأسفانه در حال حاضر امکان پرداخت وجود ندارد.\n" .
                                   "لطفاً بعداً تلاش کنید.\n\n" .
                                   "✱ تعداد ووچر: $voucherCount عدد\n" .
                                   "✱ مبلغ کل : " . number_format($totalPrice) . " تومان";

                    // Create only back button
                    $payment_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '🔙 بازگشت',
                                    'callback_data' => 'buy_voucher'
                                ]
                            ]
                        ]
                    ]);
                }

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $payment_keyboard
                ]);
                exit;
                break;
        }

        // Create back to main menu button
        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $response_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $back_keyboard
        ]);
    }

    // Handle back to main menu
    elseif ($data == 'back_to_main') {
        $first_name = $callback_query->from->first_name ?? 'کاربر';

        $main_menu_text = "سلام $first_name 👋\n\n" .
                         "به ربات صرافی بیت ووچر خوش آمدید!\n\n" .
                         "با استفاده از این ربات شما می توانید پرداخت های تلگرامی خود را راحت تر انجام دهید.\n\n" .
                         "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";

        $main_menu_keyboard = createMainMenuKeyboard();

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $main_menu_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $main_menu_keyboard
        ]);
    }

    exit;
}

// Handle regular messages
if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;

    // Save/update user data
    $userData = [
        'first_name' => $message->from->first_name ?? '',
        'last_name' => $message->from->last_name ?? '',
        'username' => $message->from->username ?? '',
        'last_interaction' => time(),
        'chat_id' => $chat_id
    ];

    if (!$db->exists('users', $user_id)) {
        $userData['step'] = 'start';
        $db->saveUser($user_id, $userData);
    } else {
        // Check if existing user has a token, if not generate one
        $existingUser = $db->getUser($user_id);
        if (!isset($existingUser['token']) || empty($existingUser['token'])) {
            $userData['token'] = $db->generateUserToken($user_id);
        }
        $db->update('users', $user_id, $userData);
    }

    if ($text == "/start") {
        $first_name = $message->from->first_name ?? 'کاربر';

        if (checkUserMembership($user_id, $required_channels)) {
            $db->updateUserStep($user_id, 'verified');

            $main_menu_text = "سلام $first_name 👋\n\n" .
                             "به ربات صرافی بیت ووچر خوش آمدید!\n\n" .
                             "با استفاده از این ربات شما می توانید پرداخت های تلگرامی خود را راحت تر انجام دهید.\n\n" .
                             "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";

            $main_menu_keyboard = createMainMenuKeyboard();
            sendmessage($chat_id, $main_menu_text, $main_menu_keyboard, 'HTML');
        } else {
            $db->updateUserStep($user_id, 'waiting_membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                "سلام $first_name 👋\n\n" .
                "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                $join_keyboard
            );
        }
    }

    // Handle voucher code input for selling
    $user = $db->getUser($user_id);
    if (isset($user['step']) && $user['step'] == 'waiting_voucher_code') {
        $voucher_code = trim($text);

        // Validate voucher code format (BV-XXXX-XXXX-XXXX-XXXX)
        if (!preg_match('/^BV-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/', $voucher_code)) {
            sendmessage($chat_id,
                "❌ فرمت کد ووچر نادرست است!\n\n" .
                "لطفا کد ووچر را با فرمت صحیح ارسال کنید:\n" .
                "مثال: BV-XXXX-XXXX-XXXX-XXXX"
            );
            return;
        }

        // Check if voucher exists
        $voucherData = findVoucherByCode($voucher_code);
        if (!$voucherData) {
            sendmessage($chat_id,
                "❌ کد ووچر وارد شده یافت نشد!\n\n" .
                "لطفا کد ووچر را بررسی کرده و مجدداً ارسال کنید."
            );
            return;
        }

        // Check if voucher is already used
        if ($voucherData['is_used']) {
            sendmessage($chat_id,
                "❌ این ووچر قبلاً استفاده شده است!\n\n" .
                "✱ تاریخ استفاده: " . $voucherData['used_at']
            );
            return;
        }

        // Check if user is the owner of the voucher
        if ($voucherData['creator_user_id'] != $user_id) {
            sendmessage($chat_id,
                "❌ شما مالک این ووچر نیستید!\n\n" .
                "✱ فقط می‌توانید ووچرهایی که خودتان ساخته‌اید را بفروشید."
            );
            return;
        }

        // Process voucher sale
        $voucher_amount = $voucherData['amount_toman'];

        // Update user balance using helper method
        $current_balance = $db->getUserBalance($user_id);
        $db->addUserBalance($user_id, $voucher_amount);
        $new_balance = $current_balance + $voucher_amount;

        // Mark voucher as used
        $voucherData['is_used'] = true;
        $voucherData['used_by'] = $user_id;
        $voucherData['used_at'] = date('Y-m-d H:i:s');
        $voucherData['status'] = 'sold';

        $voucherFile = "data/vouchers/{$voucher_code}.json";
        file_put_contents($voucherFile, json_encode($voucherData, JSON_PRETTY_PRINT));

        // Save transaction record
        $transactionId = 'TXN-' . time() . '-' . $user_id;
        $db->saveTransaction($transactionId, [
            'user_id' => $user_id,
            'voucher_code' => $voucher_code,
            'amount' => $voucher_amount,
            'currency' => 'IRR',
            'type' => 'voucher_sale',
            'status' => 'completed',
            'created_at' => time()
        ]);

        // Reset user step
        $db->updateUserStep($user_id, 'verified');

        // Send success message with back button
        $success_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        sendmessage($chat_id,
            "✅ ووچر با موفقیت فروخته شد!\n\n" .
            "✱ مبلغ: " . number_format($voucher_amount) . " تومان\n" .
            "✱ موجودی جدید: " . number_format($new_balance) . " تومان\n" .
            "✱ کد ووچر: <code>$voucher_code</code>\n\n" .
            "✱ مبلغ به کیف پول شما اضافه شد.",
            $success_keyboard,
            'HTML'
        );
    }
}
?>